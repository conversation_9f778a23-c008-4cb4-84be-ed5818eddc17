/**
 * 高性能纯滚动进度跟踪器
 * 专注滚动进度计算和目录高亮，移除时间跟踪以获得最佳性能
 */

import { ChapterSection } from './learning-utils'

// 简化的进度数据结构，移除所有时间相关字段
export interface SimpleProgress {
  tutorialId: number
  progressPercentage: number
  scrollProgress: number
  currentSection: string
  lastAccessed: string
}

export interface OptimizedScrollConfig {
  tutorialId: number
  sections: ChapterSection[]
  onProgressUpdate: (progress: SimpleProgress) => void
  onSectionChange: (sectionId: string) => void
  updateThrottle?: number // 更新节流(毫秒)，默认100ms
}

export class OptimizedScrollTracker {
  private config: OptimizedScrollConfig
  private isActive: boolean = false
  private maxScrollProgress: number = 0 // 记录最大滚动进度，防止往回退
  private currentVisibleSection: string = ''
  
  // 事件监听器
  private scrollListener?: () => void
  private intersectionObserver?: IntersectionObserver
  
  // 简化的进度数据
  private progressData: SimpleProgress

  constructor(config: OptimizedScrollConfig) {
    this.config = {
      updateThrottle: 100, // 100ms更新频率，实时响应
      ...config
    }
    
    // 初始化简化的进度数据
    this.progressData = this.loadInitialProgress()
    
    console.log('🚀 高性能滚动跟踪器初始化:', this.config.tutorialId)
  }

  /**
   * 启动跟踪
   */
  public start(): void {
    if (this.isActive) return
    
    console.log('▶️ 启动高性能滚动跟踪')
    this.isActive = true
    
    // 设置滚动监听
    this.setupScrollListener()
    
    // 设置章节监听
    this.setupSectionObserver()
    
    // 立即计算一次进度
    this.updateScrollProgress()
  }

  /**
   * 停止跟踪
   */
  public stop(): void {
    if (!this.isActive) return
    
    console.log('⏹️ 停止高性能滚动跟踪')
    this.isActive = false
    
    // 清理监听器
    this.cleanup()
  }

  /**
   * 设置滚动监听器 - 高频率实时更新
   */
  private setupScrollListener(): void {
    if (typeof window === 'undefined') return

    this.scrollListener = this.throttle(() => {
      if (this.isActive) {
        this.updateScrollProgress()
      }
    }, this.config.updateThrottle)
    
    window.addEventListener('scroll', this.scrollListener, { passive: true })
  }

  /**
   * 设置章节可见性监听器 - 用于目录高亮
   */
  private setupSectionObserver(): void {
    if (typeof window === 'undefined') return

    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        // 找到最靠近顶部的可见章节
        let topMostSection: {element: Element, top: number} | null = null
        
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const rect = entry.boundingClientRect
            const distanceFromTop = Math.abs(rect.top)
            
            if (!topMostSection || distanceFromTop < topMostSection.top) {
              topMostSection = {
                element: entry.target,
                top: distanceFromTop
              }
            }
          }
        })

        if (topMostSection) {
          const sectionId = this.getSectionId(topMostSection.element)
          if (sectionId && sectionId !== this.currentVisibleSection) {
            this.currentVisibleSection = sectionId
            this.progressData.currentSection = sectionId
            
            console.log(`👁️ 当前章节: ${sectionId}`)
            this.config.onSectionChange(sectionId)
            
            // 立即保存并通知更新
            this.saveProgress()
            this.config.onProgressUpdate(this.progressData)
          }
        }
      },
      {
        threshold: [0.1, 0.3, 0.5],
        rootMargin: '-10% 0px -10% 0px' // 上下留10%边距，确保准确检测
      }
    )

    // 监听所有章节元素
    this.observeAllSections()
  }

  /**
   * 监听所有章节
   */
  private observeAllSections(): void {
    if (!this.intersectionObserver) return

    // 通过选择器找到所有章节元素
    const sectionSelectors = [
      'h1, h2, h3, h4, h5, h6',
      '[data-section]',
      '[data-subsection]'
    ]

    sectionSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector)
      elements.forEach(element => {
        this.intersectionObserver!.observe(element)
      })
    })

    console.log(`🎯 开始监听章节元素，共 ${document.querySelectorAll('h1, h2, h3, h4, h5, h6').length} 个标题`)
  }

  /**
   * 更新滚动进度 - 核心功能，高度优化
   */
  private updateScrollProgress(): void {
    if (typeof window === 'undefined' || !this.isActive) return

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight
    const clientHeight = document.documentElement.clientHeight
    
    const maxScroll = scrollHeight - clientHeight
    let currentScrollProgress = 0
    
    if (maxScroll > 0) {
      currentScrollProgress = Math.min((scrollTop / maxScroll) * 100, 100)
    }
    
    // 关键：只能前进，不能后退
    if (currentScrollProgress > this.maxScrollProgress) {
      this.maxScrollProgress = currentScrollProgress
      
      // 更新进度数据（只包含必要字段）
      this.progressData.scrollProgress = this.maxScrollProgress
      this.progressData.progressPercentage = Math.round(this.maxScrollProgress)
      this.progressData.lastAccessed = new Date().toISOString()
      
      console.log(`📜 进度更新: ${this.progressData.progressPercentage}%`)
      
      // 保存并通知更新
      this.saveProgress()
      this.config.onProgressUpdate(this.progressData)
    }
  }

  /**
   * 获取章节ID
   */
  private getSectionId(element: Element): string | null {
    // 优先使用data属性
    const dataSection = element.getAttribute('data-section')
    if (dataSection) return dataSection
    
    const dataSubsection = element.getAttribute('data-subsection')
    if (dataSubsection) return dataSubsection
    
    // 使用元素ID
    if (element.id) return element.id
    
    // 为标题元素生成ID
    if (element.tagName.match(/^H[1-6]$/)) {
      const text = element.textContent?.trim()
      if (text) {
        // 生成基于内容的ID
        const id = text.substring(0, 20).replace(/[^\w\u4e00-\u9fa5]/g, '-').toLowerCase()
        if (!element.id) {
          element.id = id
        }
        return id
      }
    }
    
    return null
  }

  /**
   * 加载初始进度（简化版）
   */
  private loadInitialProgress(): SimpleProgress {
    const defaultProgress: SimpleProgress = {
      tutorialId: this.config.tutorialId,
      progressPercentage: 0,
      scrollProgress: 0,
      currentSection: '',
      lastAccessed: new Date().toISOString()
    }

    try {
      const stored = localStorage.getItem(`scroll_progress_${this.config.tutorialId}`)
      if (stored) {
        const parsed = JSON.parse(stored)
        // 恢复最大滚动进度
        this.maxScrollProgress = parsed.scrollProgress || 0
        return { ...defaultProgress, ...parsed }
      }
    } catch (error) {
      console.warn('加载进度失败:', error)
    }

    return defaultProgress
  }

  /**
   * 保存进度（简化版）
   */
  private saveProgress(): void {
    try {
      localStorage.setItem(
        `scroll_progress_${this.config.tutorialId}`,
        JSON.stringify(this.progressData)
      )
    } catch (error) {
      console.warn('保存进度失败:', error)
    }
  }

  /**
   * 高性能节流函数
   */
  private throttle<T extends (...args: any[]) => any>(func: T, wait: number): T {
    let timeout: NodeJS.Timeout | null = null
    let previous = 0
    
    return ((...args: any[]) => {
      const now = Date.now()
      const remaining = wait - (now - previous)
      
      if (remaining <= 0 || remaining > wait) {
        if (timeout) {
          clearTimeout(timeout)
          timeout = null
        }
        previous = now
        func.apply(this, args)
      } else if (!timeout) {
        timeout = setTimeout(() => {
          previous = Date.now()
          timeout = null
          func.apply(this, args)
        }, remaining)
      }
    }) as T
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    // 清理事件监听器
    if (this.scrollListener) {
      window.removeEventListener('scroll', this.scrollListener)
    }

    // 清理交叉观察器
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect()
    }
  }

  /**
   * 获取当前进度
   */
  public getProgress(): SimpleProgress {
    return { ...this.progressData }
  }

  /**
   * 手动设置当前章节
   */
  public setCurrentSection(sectionId: string): void {
    this.currentVisibleSection = sectionId
    this.progressData.currentSection = sectionId
    this.config.onSectionChange(sectionId)
    this.saveProgress()
    this.config.onProgressUpdate(this.progressData)
  }

  /**
   * 销毁跟踪器
   */
  public destroy(): void {
    this.stop()
    this.cleanup()
    console.log('🗑️ 高性能滚动跟踪器已销毁')
  }
}